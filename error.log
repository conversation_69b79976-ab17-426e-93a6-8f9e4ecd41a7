System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object.
   at AccelArgs.OnDeserializedMethod(StreamingContext context)
   --- End of inner exception stack trace ---
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Object[] arguments, Signature sig, Boolean constructor)
   at System.Reflection.RuntimeMethodInfo.UnsafeInvokeInternal(Object obj, Object[] parameters, Object[] arguments)
   at System.Reflection.RuntimeMethodInfo.Invoke(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Newtonsoft.Json.Serialization.JsonContract.<>c__DisplayClass57_0.<CreateSerializationCallback>b__0(Object o, StreamingContext context)
   at Newtonsoft.Json.Serialization.JsonContract.InvokeOnDeserialized(Object o, StreamingContext context)
   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.OnDeserialized(JsonReader reader, JsonContract contract, Object value)
   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.PopulateObject(Object newObject, JsonReader reader, JsonObjectContract contract, JsonProperty member, String id)
   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.CreateObject(JsonReader reader, Type objectType, JsonContract contract, JsonProperty member, JsonContainerContract containerContract, JsonProperty containerMember, Object existingValue)
   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.CreateValueInternal(JsonReader reader, Type objectType, JsonContract contract, JsonProperty member, JsonContainerContract containerContract, JsonProperty containerMember, Object existingValue)
   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.SetPropertyValue(JsonProperty property, JsonConverter propertyConverter, JsonContainerContract containerContract, JsonProperty containerProperty, JsonReader reader, Object target)
   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.PopulateObject(Object newObject, JsonReader reader, JsonObjectContract contract, JsonProperty member, String id)
   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.CreateObject(JsonReader reader, Type objectType, JsonContract contract, JsonProperty member, JsonContainerContract containerContract, JsonProperty containerMember, Object existingValue)
   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.CreateValueInternal(JsonReader reader, Type objectType, JsonContract contract, JsonProperty member, JsonContainerContract containerContract, JsonProperty containerMember, Object existingValue)
   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.PopulateList(IList list, JsonReader reader, JsonArrayContract contract, JsonProperty containerProperty, String id)
   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.CreateList(JsonReader reader, Type objectType, JsonContract contract, JsonProperty member, Object existingValue, String id)
   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.CreateValueInternal(JsonReader reader, Type objectType, JsonContract contract, JsonProperty member, JsonContainerContract containerContract, JsonProperty containerMember, Object existingValue)
   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.SetPropertyValue(JsonProperty property, JsonConverter propertyConverter, JsonContainerContract containerContract, JsonProperty containerProperty, JsonReader reader, Object target)
   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.PopulateObject(Object newObject, JsonReader reader, JsonObjectContract contract, JsonProperty member, String id)
   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.CreateObject(JsonReader reader, Type objectType, JsonContract contract, JsonProperty member, JsonContainerContract containerContract, JsonProperty containerMember, Object existingValue)
   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.CreateValueInternal(JsonReader reader, Type objectType, JsonContract contract, JsonProperty member, JsonContainerContract containerContract, JsonProperty containerMember, Object existingValue)
   at Newtonsoft.Json.Serialization.JsonSerializerInternalReader.Deserialize(JsonReader reader, Type objectType, Boolean checkAdditionalContent)
   at Newtonsoft.Json.JsonSerializer.DeserializeInternal(JsonReader reader, Type objectType)
   at Newtonsoft.Json.JsonConvert.DeserializeObject(String value, Type type, JsonSerializerSettings settings)
   at Newtonsoft.Json.JsonConvert.DeserializeObject[T](String value, JsonSerializerSettings settings)
   at DriverConfig.Convert(String json)
   at grapher.Models.Serialized.SettingsManager.InitActiveAndGetUserConfig()
   at grapher.Models.Serialized.SettingsManager..ctor(Field dpiField, Field pollRateField, ToolStripMenuItem autoWrite, ToolStripMenuItem showLastMouseMove, ToolStripMenuItem showVelocityAndGain, ToolStripMenuItem themeMenu, Form form)
   at grapher.Models.AccelGUIFactory.Construct(RawAcceleration form, Chart accelerationChart, Chart accelerationChartY, Chart velocityChart, Chart velocityChartY, Chart gainChart, Chart gainChartY, TableLayoutPanel chartContainer, ThemeableComboBox accelTypeDropX, ThemeableComboBox accelTypeDropY, ThemeableComboBox lutApplyDropdownX, ThemeableComboBox lutApplyDropdownY, ThemeableComboBox capTypeDropdownXClassic, ThemeableComboBox capTypeDropdownYClassic, ThemeableComboBox capTypeDropdownXPower, ThemeableComboBox capTypeDropdownYPower, Button writeButton, Button toggleButton, ToolStripMenuItem showVelocityGainToolStripMenuItem, ToolStripMenuItem showLastMouseMoveMenuItem, ToolStripMenuItem autoWriteMenuItem, ToolStripMenuItem deviceMenuItem, ToolStripMenuItem scaleMenuItem, ToolStripMenuItem themeMenuItem, ToolStripTextBox dpiTextBox, ToolStripTextBox pollRateTextBox, Panel directionalityPanel, ThemeableTextBox sensitivityBoxX, ThemeableTextBox sensitivityBoxY, ThemeableTextBox rotationBox, ThemeableTextBox inCapBoxXClassic, ThemeableTextBox inCapBoxYClassic, ThemeableTextBox outCapBoxXClassic, ThemeableTextBox outCapBoxYClassic, ThemeableTextBox inCapBoxXPower, ThemeableTextBox inCapBoxYPower, ThemeableTextBox outCapBoxXPower, ThemeableTextBox outCapBoxYPower, ThemeableTextBox inputJumpBoxX, ThemeableTextBox inputJumpBoxY, ThemeableTextBox outputJumpBoxX, ThemeableTextBox outputJumpBoxY, ThemeableTextBox inputOffsetBoxX, ThemeableTextBox inputOffsetBoxY, ThemeableTextBox outputOffsetBoxX, ThemeableTextBox outputOffsetBoxY, ThemeableTextBox accelerationBoxX, ThemeableTextBox accelerationBoxY, ThemeableTextBox decayRateBoxX, ThemeableTextBox decayRateBoxY, ThemeableTextBox gammaBoxX, ThemeableTextBox gammaBoxY, ThemeableTextBox smoothBoxX, ThemeableTextBox smoothBoxY, ThemeableTextBox scaleBoxX, ThemeableTextBox scaleBoxY, ThemeableTextBox limitBoxX, ThemeableTextBox limitBoxY, ThemeableTextBox powerClassicBoxX, ThemeableTextBox powerClassicBoxY, ThemeableTextBox expBoxX, ThemeableTextBox expBoxY, ThemeableTextBox syncSpeedBoxX, ThemeableTextBox syncSpeedBoxY, ThemeableTextBox domainBoxX, ThemeableTextBox domainBoxY, ThemeableTextBox rangeBoxX, ThemeableTextBox rangeBoxY, ThemeableTextBox lpNormBox, CheckBox sensXYLock, CheckBox byComponentXYLock, CheckBox fakeBox, CheckBox wholeCheckBox, CheckBox byComponentCheckBox, CheckBox gainSwitchX, CheckBox gainSwitchY, RichTextBox xLutActiveValuesBox, RichTextBox yLutActiveValuesBox, RichTextBox xLutPointsBox, RichTextBox yLutPointsBox, Label lockXYLabel, Label sensitivityLabel, Label yxRatioLabel, Label rotationLabel, Label inCapLabelXClassic, Label inCapLabelYClassic, Label outCapLabelXClassic, Label outCapLabelYClassic, Label capTypeLabelXClassic, Label capTypeLabelYClassic, Label inCapLabelXPower, Label inCapLabelYPower, Label outCapLabelXPower, Label outCapLabelYPower, Label capTypeLabelXPower, Label capTypeLabelYPower, Label inputJumpLabelX, Label inputJumpLabelY, Label outputJumpLabelX, Label outputJumpLabelY, Label inputOffsetLabelX, Label inputOffsetLabelY, Label outputOffsetLabelX, Label outputOffsetLabelY, Label constantOneLabelX, Label constantOneLabelY, Label decayRateLabelX, Label decayRateLabelY, Label gammaLabelX, Label gammaLabelY, Label smoothLabelX, Label smoothLabelY, Label scaleLabelX, Label scaleLabelY, Label limitLabelX, Label limitLabelY, Label powerClassicLabelX, Label powerClassicLabelY, Label expLabelX, Label expLabelY, Label lutTextLabelX, Label lutTextLabelY, Label constantThreeLabelX, Label constantThreeLabelY, Label activeValueTitleX, Label activeValueTitleY, Label sensitivityActiveLabel, Label yxRatioActiveLabel, Label rotationActiveLabel, Label inCapActiveXLabelClassic, Label inCapActiveYLabelClassic, Label outCapActiveXLabelClassic, Label outCapActiveYLabelClassic, Label capTypeActiveXLabelClassic, Label capTypeActiveYLabelClassic, Label inCapActiveXLabelPower, Label inCapActiveYLabelPower, Label outCapActiveXLabelPower, Label outCapActiveYLabelPower, Label capTypeActiveXLabelPower, Label capTypeActiveYLabelPower, Label inputJumpActiveLabelX, Label inputJumpActiveLabelY, Label outputJumpActiveLabelX, Label outputJumpActiveLabelY, Label inputOffsetActiveLabelX, Label inputOffsetActiveLabelY, Label outputOffsetActiveLabelX, Label outputOffsetActiveLabelY, Label accelerationActiveLabelX, Label accelerationActiveLabelY, Label decayRateActiveLabelX, Label decayRateActiveLabelY, Label gammaActiveLabelX, Label gammaActiveLabelY, Label smoothActiveLabelX, Label smoothActiveLabelY, Label scaleActiveLabelX, Label scaleActiveLabelY, Label limitActiveLabelX, Label limitActiveLabelY, Label powerClassicActiveLabelX, Label powerClassicActiveLabelY, Label expActiveLabelX, Label expActiveLabelY, Label syncSpeedActiveLabelX, Label syncSpeedActiveLabelY, Label accelTypeActiveLabelX, Label accelTypeActiveLabelY, Label gainSwitchActiveLabelX, Label gainSwitchActiveLabelY, Label optionSetXTitle, Label optionSetYTitle, Label mouseLabel, Button directionalityLabel, Label directionalityX, Label directionalityY, Label direcionalityActiveValueTitle, Label lpNormLabel, Label lpNormActiveLabel, Label domainLabel, Label domainActiveValueX, Label domainActiveValueY, Label rangeLabel, Label rangeActiveValueX, Label rangeActiveValueY, Label lutApplyLabelX, Label lutApplyLabelY, Label lutApplyActiveValueX, Label lutApplyActiveValueY)
   at grapher.RawAcceleration..ctor()
   at grapher.Program.Main()