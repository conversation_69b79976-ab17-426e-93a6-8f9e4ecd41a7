# Raw Accel

Raw Accel is a Windows 10 64-bit driver which allows for the acceleration of mouse input in the raw input stream. It started as a replacement for [InterAccel](https://github.com/KovaaK/InterAccel) and has been extended with more acceleration types, charts, and other features.

## Anti-Cheat Friendly

[Releases](https://github.com/a1xd/rawaccel/releases/latest) of the Raw Accel driver are signed and run in system space. Raw Accel only modifies mouse input by a constant set of formulas, and adds a one-second delay when changing settings in order to mitigate its abuse.

## Getting Help

For an overview of everything Raw Accel has to offer, please see the [guide](doc/Guide.md). For questions, see the [FAQ](doc/FAQ.md) first.

## Development

Development of Raw Accel is ongoing at https://github.com/a1xd/rawaccel. Bug reports and pull requests are always welcome.  Join [our Discord server](https://discord.gg/7pQh8zH) if you want to stay updated with releases or say hello.

## Credits
simon - Driver & Acceleration Logic  
\_m00se\_ - GUI, Gain features, Acceleration types  
Sidiouth  - Primary tester and sounding board  
TauntyArmordillo - Originator of the alternate curve ideas (Natural and Motivity types)  
Kovaak - Brought us together
