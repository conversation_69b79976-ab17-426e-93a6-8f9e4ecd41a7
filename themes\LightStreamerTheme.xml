<ColorScheme xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><Name>Light Streamer Theme</Name><ChartBackground Web="Green" /><ChartForeground Web="White" /><Primary Web="Blue" /><Secondary Web="Orange" /><MouseMovement Web="#C00000" /><Field Web="White" /><OnField Web="#404040" /><OnFocusedField Web="#101010" /><EditedField Web="AntiqueWhite" /><OnEditedField Web="DarkGray" /><ButtonFace Web="#E1E1E1" /><ButtonBorder Web="#ADADAD" /><Control Web="#F0F0F0" /><ControlBorder Web="#7A7A7A" /><OnControl Web="#404040" /><DisabledControl Web="#EEEEEE" /><OnDisabledControl Web="Gray" /><Background Web="White" /><OnBackground Web="#404040" /><Surface Web="#F0F0F0" /><MenuBackground Web="#E3E3E3" /><MenuSelectedBorder Web="#0078D7" /><MenuSelected Web="#B3D7F3" /><CheckBoxBackground Web="#C4E3FA" /><CheckBoxBorder Web="#0078D7" /><CheckBoxHover Web="#71A6CF" /><CheckBoxChecked Web="#5364BE" /><UseAccentGradientsForCheckboxes>false</UseAccentGradientsForCheckboxes><UseAccentGradientsForButtons>false</UseAccentGradientsForButtons></ColorScheme>